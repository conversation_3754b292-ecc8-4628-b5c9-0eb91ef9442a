# CPP (Component Preview Protocol)

## Introduction

The Component Preview Protocol (CPP) is an open protocol designed to enable different tools to work with UI components and previews across various platforms and technologies. Similar to Storybook's [Component Story Format (CSF)](https://storybook.js.org/docs/8/api/csf), CPP provides a standardized way to define, discover, and interact with UI component previews. However, unlike CSF which is JavaScript-specific, CPP is language-agnostic and operates cross-process using JSON-RPC, similar to the Model Context Protocol (MCP).

CPP enables:
- **Cross-platform component discovery**: Find UI components and their previews across different platforms (.NET MAUI, WPF, etc.)
- **Tool interoperability**: Allow different development tools to work with the same component definitions
- **Language independence**: Support components written in any language that can implement the protocol
- **Remote communication**: Enable tools to communicate with applications running in different processes or even on different machines

## Protocol Overview

CPP uses JSON-RPC 2.0 over TCP for communication between tools (like HotPreview DevTools) and applications. The protocol defines two main service interfaces:

1. **IPreviewAppService**: Implemented by applications to expose their components and previews
2. **IPreviewAppControllerService**: Implemented by tools to receive notifications from applications

## JSON-RPC Schema

### IPreviewAppService Interface

The main service interface that applications must implement to expose their UI components and previews.

#### Methods

##### components/list
Retrieves all available UI components and their associated previews.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "components/list",
  "params": {}
}
```

**Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": [
    {
      "name": "MyApp.Views.ProductCard",
      "uiComponentKind": "control",
      "displayName": "Product Card",
      "previews": [
        {
          "previewType": "staticMethod",
          "name": "DefaultPreview",
          "displayName": "Default",
          "autoGenerated": false
        }
      ]
    }
  ]
}
```

##### components/get
Retrieves information for a specific UI component.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "method": "components/get",
  "params": {
    "componentName": "MyApp.Views.ProductCard"
  }
}
```

**Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "result": {
    "name": "MyApp.Views.ProductCard",
    "uiComponentKind": "control",
    "displayName": "Product Card",
    "previews": [
      {
        "previewType": "staticMethod",
        "name": "DefaultPreview",
        "displayName": "Default",
        "autoGenerated": false
      }
    ]
  }
}
```

##### previews/navigate
Navigates the application to display a specific preview.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 3,
  "method": "previews/navigate",
  "params": {
    "componentName": "MyApp.Views.ProductCard",
    "previewName": "DefaultPreview"
  }
}
```

**Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 3,
  "result": null
}
```

##### previews/snapshot
Captures a visual snapshot of a specific preview.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 4,
  "method": "previews/snapshot",
  "params": {
    "uiComponentName": "MyApp.Views.ProductCard",
    "previewName": "DefaultPreview"
  }
}
```

**Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 4,
  "result": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
}
```

**Note:** The snapshot result is returned as a base64-encoded byte array representing the PNG image data.

##### commands/list
Retrieves all available preview commands.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 5,
  "method": "commands/list",
  "params": {}
}
```

**Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 5,
  "result": [
    {
      "name": "MyApp.Commands.RefreshData",
      "displayName": "Refresh Data"
    }
  ]
}
```

##### commands/get
Retrieves information for a specific command.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 6,
  "method": "commands/get",
  "params": {
    "commandName": "MyApp.Commands.RefreshData"
  }
}
```

**Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 6,
  "result": {
    "name": "MyApp.Commands.RefreshData",
    "displayName": "Refresh Data"
  }
}
```

##### commands/invoke
Executes a preview command.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 7,
  "method": "commands/invoke",
  "params": {
    "commandName": "MyApp.Commands.RefreshData"
  }
}
```

**Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 7,
  "result": null
}
```

### IPreviewAppControllerService Interface

The controller service interface that tools implement to receive notifications from applications.

#### Methods

##### registerApp
Registers an application with the tool, providing project and platform information.

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "registerApp",
  "params": {
    "projectPath": "/projects/myProject/myProject.csproj",
    "platformName": "MAUI"
  }
}
```

##### notifications/components/listChanged
Notifies the tool that the available components/previews have changed (e.g., due to code changes).

**Request:**
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "method": "notifications/components/listChanged",
  "params": {}
}
```

## Data Types

### UIComponentInfo
Represents information about a UI component and its previews.

```typescript
interface UIComponentInfo {
  name: string;                    // Fully qualified component name
  uiComponentKind: string;         // "page", "control", or "unknown"
  displayName?: string;            // Optional display name override
  previews: PreviewInfo[];         // Array of available previews
}
```

### PreviewInfo
Represents information about a specific preview.

```typescript
interface PreviewInfo {
  previewType: string;       // "class" or "staticMethod"
  name: string;              // Preview identifier
  displayName?: string;      // Optional display name override
  autoGenerated: boolean;    // Whether the preview was auto-generated
}
```

### PreviewCommandInfo
Represents information about an executable command.

```typescript
interface PreviewCommandInfo {
  name: string;              // Fully qualified command name
  displayName?: string;      // Optional display name override
}
```

## Implementation Notes

- All string parameters should use fully qualified names for components and commands to ensure uniqueness
- Binary data (like snapshots) are returned as byte arrays and should be base64-encoded when transmitted over JSON-RPC
- The protocol supports both auto-generated and manually defined previews
- Component kinds help tools categorize and display components appropriately
- Display names provide human-readable alternatives to technical names
- The `components/get` and `commands/get` methods return `null` if the requested component or command is not found

## Connection Establishment

1. **Application Startup**: The application connects to the tool via TCP
2. **Registration**: Application calls `registerApp` to identify itself
3. **Discovery**: Tool calls `components/list` to discover available components
4. **Interaction**: Tool can navigate to previews, capture snapshots, and execute commands
5. **Change Notification**: Application calls `notifications/components/listChanged` when components/previews change
